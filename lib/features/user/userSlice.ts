import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// Define the user interface
export interface User {
  id: string
  name: string
  email: string
  role: 'admin' | 'user' | 'guest'
}

// Define the initial state interface
export interface UserState {
  currentUser: User | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
}

// Define the initial state
const initialState: UserState = {
  currentUser: null,
  isAuthenticated: false,
  loading: false,
  error: null,
}

// Create the user slice
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
      if (action.payload) {
        state.error = null
      }
    },
    // Set user data (login)
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload
      state.isAuthenticated = true
      state.loading = false
      state.error = null
    },
    // Clear user data (logout)
    clearUser: (state) => {
      state.currentUser = null
      state.isAuthenticated = false
      state.loading = false
      state.error = null
    },
    // Set error
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
      state.loading = false
    },
    // Update user profile
    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload }
      }
    },
  },
})

// Export action creators
export const { setLoading, setUser, clearUser, setError, updateUserProfile } = userSlice.actions

// Export selectors
export const selectCurrentUser = (state: { user: UserState }) => state.user.currentUser
export const selectIsAuthenticated = (state: { user: UserState }) => state.user.isAuthenticated
export const selectUserLoading = (state: { user: UserState }) => state.user.loading
export const selectUserError = (state: { user: UserState }) => state.user.error

// Export the reducer
export default userSlice.reducer
