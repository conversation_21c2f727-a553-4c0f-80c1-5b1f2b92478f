import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// Define the initial state interface
export interface CounterState {
  value: number
  status: 'idle' | 'loading' | 'succeeded' | 'failed'
}

// Define the initial state
const initialState: CounterState = {
  value: 0,
  status: 'idle',
}

// Create the counter slice
export const counterSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    // Increment the counter by 1
    increment: (state) => {
      state.value += 1
    },
    // Decrement the counter by 1
    decrement: (state) => {
      state.value -= 1
    },
    // Increment the counter by a specific amount
    incrementByAmount: (state, action: PayloadAction<number>) => {
      state.value += action.payload
    },
    // Reset the counter to 0
    reset: (state) => {
      state.value = 0
      state.status = 'idle'
    },
    // Set the loading status
    setStatus: (state, action: PayloadAction<CounterState['status']>) => {
      state.status = action.payload
    },
  },
})

// Export action creators
export const { increment, decrement, incrementByAmount, reset, setStatus } = counterSlice.actions

// Export selectors
export const selectCount = (state: { counter: CounterState }) => state.counter.value
export const selectStatus = (state: { counter: CounterState }) => state.counter.status

// Export the reducer
export default counterSlice.reducer
