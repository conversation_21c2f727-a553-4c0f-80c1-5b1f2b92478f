'use client'

import { useAppSelector, useAppDispatch } from '../../lib/hooks'
import { increment, decrement, incrementByAmount, reset, setStatus } from '../../lib/features/counter/counterSlice'
import { useState } from 'react'

export default function Counter() {
  const count = useAppSelector((state) => state.counter.value)
  const status = useAppSelector((state) => state.counter.status)
  const dispatch = useAppDispatch()
  const [incrementAmount, setIncrementAmount] = useState('2')

  const incrementValue = Number(incrementAmount) || 0

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md mx-auto">
      <h2 className="text-2xl font-bold text-center mb-6 text-gray-800 dark:text-white">
        Redux Counter Example
      </h2>
      
      <div className="text-center mb-6">
        <span className="text-4xl font-bold text-blue-600 dark:text-blue-400">
          {count}
        </span>
        <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
          Status: <span className="font-semibold">{status}</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Basic increment/decrement buttons */}
        <div className="flex gap-2 justify-center">
          <button
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
            onClick={() => dispatch(increment())}
          >
            +1
          </button>
          <button
            className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors"
            onClick={() => dispatch(decrement())}
          >
            -1
          </button>
        </div>

        {/* Increment by amount */}
        <div className="flex gap-2 items-center justify-center">
          <input
            className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-center bg-white dark:bg-gray-700 text-gray-800 dark:text-white"
            value={incrementAmount}
            onChange={(e) => setIncrementAmount(e.target.value)}
            type="number"
          />
          <button
            className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            onClick={() => dispatch(incrementByAmount(incrementValue))}
          >
            Add Amount
          </button>
        </div>

        {/* Status buttons */}
        <div className="flex gap-2 justify-center flex-wrap">
          <button
            className="px-3 py-1 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md text-sm transition-colors"
            onClick={() => dispatch(setStatus('loading'))}
          >
            Set Loading
          </button>
          <button
            className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded-md text-sm transition-colors"
            onClick={() => dispatch(setStatus('succeeded'))}
          >
            Set Success
          </button>
          <button
            className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-md text-sm transition-colors"
            onClick={() => dispatch(setStatus('failed'))}
          >
            Set Failed
          </button>
        </div>

        {/* Reset button */}
        <div className="text-center">
          <button
            className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors"
            onClick={() => dispatch(reset())}
          >
            Reset
          </button>
        </div>
      </div>

      {/* Usage explanation */}
      <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-md">
        <h3 className="font-semibold text-sm text-gray-800 dark:text-white mb-2">
          Redux Usage Examples:
        </h3>
        <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
          <li>• <code>useAppSelector</code> to read state</li>
          <li>• <code>useAppDispatch</code> to dispatch actions</li>
          <li>• Actions: increment, decrement, incrementByAmount</li>
          <li>• State includes value and status</li>
        </ul>
      </div>
    </div>
  )
}
