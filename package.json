{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.540.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "tailwind": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}